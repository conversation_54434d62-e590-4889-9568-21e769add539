use crate::database::DatabaseConfig;
use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use surrealdb::{engine::remote::ws::Client, sql::Thing, Surreal};

/// SurrealDB客户端封装
#[derive(Clone)]
pub struct SurrealDBClient {
    /// 数据库连接
    db: Arc<Surreal<Client>>,
    /// 配置信息
    config: DatabaseConfig,
}

impl SurrealDBClient {
    /// 创建新的SurrealDB客户端
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        // 验证配置
        config.validate()?;

        // 创建连接
        let db = config.connect().await?;

        let client = Self {
            db: Arc::new(db),
            config,
        };

        // 初始化数据库结构
        // 暂时注释掉复杂的初始化逻辑，先测试基本连接
        // client.initialize_schema().await?;
        tracing::info!("SurrealDB客户端创建成功，跳过schema初始化");

        Ok(client)
    }

    /// 获取数据库连接
    pub fn db(&self) -> &Surreal<Client> {
        &self.db
    }

    /// 初始化数据库结构
    async fn initialize_schema(&self) -> Result<()> {
        tracing::info!("初始化SurrealDB数据库结构...");

        // 定义DataSource表结构
        self.db
            .query(
                "
            DEFINE TABLE datasource SCHEMAFULL;

            DEFINE FIELD id ON TABLE datasource TYPE string;
            DEFINE FIELD name ON TABLE datasource TYPE string;
            DEFINE FIELD description ON TABLE datasource TYPE option<string>;
            DEFINE FIELD connector_config ON TABLE datasource TYPE object;
            DEFINE FIELD created_at ON TABLE datasource TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated_at ON TABLE datasource TYPE datetime DEFAULT time::now();
            DEFINE FIELD deleted_at ON TABLE datasource TYPE option<datetime>;

            DEFINE INDEX datasource_name_unique ON TABLE datasource FIELDS name UNIQUE;
            DEFINE INDEX datasource_created_at ON TABLE datasource FIELDS created_at;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("初始化表结构失败: {}", e)))?;

        // 定义搜索分析器
        self.db
            .query(
                "
            DEFINE ANALYZER basic_analyzer
                TOKENIZERS blank, punct, camel, class
                FILTERS lowercase, ascii;

            DEFINE ANALYZER chinese_analyzer
                TOKENIZERS blank, punct, class
                FILTERS lowercase, ascii, ngram(2,3);
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("初始化分析器失败: {}", e)))?;

        // 定义搜索索引
        self.db
            .query(
                "
            DEFINE INDEX datasource_search ON TABLE datasource
                FIELDS name, description
                SEARCH ANALYZER basic_analyzer BM25 HIGHLIGHTS;

            DEFINE INDEX datasource_chinese_search ON TABLE datasource
                FIELDS name, description
                SEARCH ANALYZER chinese_analyzer BM25;
        ",
            )
            .await
            .map_err(|e| crate::error::Error::Database(format!("初始化搜索索引失败: {}", e)))?;

        tracing::info!("SurrealDB数据库结构初始化完成");
        Ok(())
    }

    /// 创建记录
    pub async fn create<T>(&self, table: &str, data: &T) -> Result<T>
    where
        T: Serialize + for<'de> Deserialize<'de>,
    {
        let records: Vec<T> = self
            .db
            .create(table)
            .content(data)
            .await
            .map_err(|e| crate::error::Error::Database(format!("创建记录失败: {}", e)))?;

        records
            .into_iter()
            .next()
            .ok_or_else(|| crate::error::Error::Database("创建记录失败: 未返回记录".to_string()))
    }

    /// 根据ID查询记录
    pub async fn select<T>(&self, thing: &Thing) -> Result<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let result = self
            .db
            .select(thing)
            .await
            .map_err(|e| crate::error::Error::Database(format!("查询记录失败: {}", e)))?;

        Ok(result)
    }

    /// 更新记录
    pub async fn update<T>(&self, thing: &Thing, data: &T) -> Result<Option<T>>
    where
        T: Serialize + for<'de> Deserialize<'de>,
    {
        let result = self
            .db
            .update(thing)
            .content(data)
            .await
            .map_err(|e| crate::error::Error::Database(format!("更新记录失败: {}", e)))?;

        Ok(result)
    }

    /// 删除记录
    pub async fn delete<T>(&self, thing: &Thing) -> Result<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let result = self
            .db
            .delete(thing)
            .await
            .map_err(|e| crate::error::Error::Database(format!("删除记录失败: {}", e)))?;

        Ok(result)
    }

    /// 执行自定义查询
    pub async fn query(&self, sql: &str) -> Result<surrealdb::Response> {
        let result = self
            .db
            .query(sql)
            .await
            .map_err(|e| crate::error::Error::Database(format!("执行查询失败: {}", e)))?;

        Ok(result)
    }

    /// 执行带参数的查询
    pub async fn query_with_params(
        &self,
        sql: &str,
        params: &serde_json::Value,
    ) -> Result<surrealdb::Response> {
        let mut query = self.db.query(sql);

        // 绑定参数
        if let Some(obj) = params.as_object() {
            for (key, value) in obj {
                query = query.bind((key.as_str(), value));
            }
        }

        let result = query
            .await
            .map_err(|e| crate::error::Error::Database(format!("执行参数化查询失败: {}", e)))?;

        Ok(result)
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<bool> {
        match self.db.query("SELECT 1 as test").await {
            Ok(_) => Ok(true),
            Err(e) => {
                tracing::error!("SurrealDB健康检查失败: {}", e);
                Ok(false)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    // 注意：这些测试需要运行中的SurrealDB实例
    #[tokio::test]
    #[ignore] // 默认忽略，需要手动运行
    async fn test_client_creation() {
        let config = DatabaseConfig::default();
        let client = SurrealDBClient::new(config).await;
        assert!(client.is_ok());
    }

    #[tokio::test]
    #[ignore]
    async fn test_health_check() {
        let config = DatabaseConfig::default();
        let client = SurrealDBClient::new(config).await.unwrap();
        let health = client.health_check().await.unwrap();
        assert!(health);
    }
}

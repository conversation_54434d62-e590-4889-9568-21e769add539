use surrealdb::{
    engine::remote::ws::{Client, Ws},
    opt::auth::Root,
    Surreal,
};

/// 完全独立的SurrealDB连接测试
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 独立SurrealDB连接测试");
    println!("========================");

    // 连接参数
    let url = "127.0.0.1:8000";
    let username = "root";
    let password = "root";
    let namespace = "coco";
    let database = "main";

    println!("📋 连接参数:");
    println!("   URL: {}", url);
    println!("   用户名: {}", username);
    println!("   命名空间: {}", namespace);
    println!("   数据库: {}", database);
    println!();

    // 步骤1: 创建连接
    println!("🔗 步骤1: 创建WebSocket连接...");
    let db = match Surreal::new::<Ws>(url).await {
        Ok(db) => {
            println!("✅ WebSocket连接创建成功!");
            db
        }
        Err(e) => {
            println!("❌ WebSocket连接失败: {}", e);
            return Err(e.into());
        }
    };

    // 步骤2: 认证
    println!("🔐 步骤2: Root用户认证...");
    match db
        .signin(Root {
            username,
            password,
        })
        .await
    {
        Ok(_) => {
            println!("✅ 认证成功!");
        }
        Err(e) => {
            println!("❌ 认证失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤3: 选择命名空间和数据库
    println!("📁 步骤3: 选择命名空间和数据库...");
    match db.use_ns(namespace).use_db(database).await {
        Ok(_) => {
            println!("✅ 命名空间和数据库选择成功!");
        }
        Err(e) => {
            println!("❌ 选择失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤4: 测试基本查询
    println!("📊 步骤4: 测试基本查询...");
    match db.query("SELECT 1 as test").await {
        Ok(mut response) => {
            println!("✅ 基本查询成功!");
            let result: Option<serde_json::Value> = response.take(0)?;
            println!("   查询结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 基本查询失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤5: 测试创建记录
    println!("📝 步骤5: 测试创建记录...");
    let create_sql = "CREATE test_standalone SET name = 'standalone_test', value = 123";
    match db.query(create_sql).await {
        Ok(mut response) => {
            println!("✅ 创建记录成功!");
            let result: Option<serde_json::Value> = response.take(0)?;
            println!("   创建结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 创建记录失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤6: 查询记录
    println!("🔍 步骤6: 查询刚创建的记录...");
    match db.query("SELECT * FROM test_standalone").await {
        Ok(mut response) => {
            println!("✅ 查询记录成功!");
            let result: Vec<serde_json::Value> = response.take(0)?;
            println!("   查询结果: {:?}", result);
        }
        Err(e) => {
            println!("❌ 查询记录失败: {}", e);
            return Err(e.into());
        }
    }

    // 步骤7: 清理
    println!("🧹 步骤7: 清理测试数据...");
    match db.query("DELETE test_standalone").await {
        Ok(_) => {
            println!("✅ 清理成功!");
        }
        Err(e) => {
            println!("⚠️ 清理失败: {}", e);
        }
    }

    println!();
    println!("🎉 所有测试完成!");
    println!("✅ SurrealDB连接工作正常，可以开始开发!");
    println!("✅ 新版本SurrealDB 2.3.7 API工作正常");

    Ok(())
}
